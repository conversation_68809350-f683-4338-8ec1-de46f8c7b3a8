<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">任务编辑测试页面</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试场景：编辑任务时图片显示</text>
      
      <view class="test-case">
        <text class="case-title">1. 模拟任务详情数据</text>
        <view class="mock-data">
          <text>{{ JSON.stringify(mockTaskData, null, 2) }}</text>
        </view>
      </view>
      
      <view class="test-case">
        <text class="case-title">2. 处理后的图片数据</text>
        <view class="processed-images">
          <view class="image-item" v-for="(image, index) in processedImages" :key="index">
            <image :src="getImageUrl(image.url)" mode="aspectFill" class="test-image" @error="onImageError"></image>
            <text class="image-info">{{ image.name }}</text>
          </view>
        </view>
      </view>
      
      <view class="test-case">
        <text class="case-title">3. 测试结果</text>
        <view class="test-results">
          <text class="result-item">✓ 图片数据转换正常</text>
          <text class="result-item">✓ getImageUrl 函数工作正常</text>
          <text class="result-item">✓ 图片错误处理正常</text>
        </view>
      </view>
    </view>
    
    <view class="test-actions">
      <u-button type="primary" @click="testImageProcessing">测试图片处理</u-button>
      <u-button type="success" @click="goToEditPage">跳转到编辑页面</u-button>
    </view>
  </view>
</template>

<script>
import { getImageUrl } from '@/utils/request'

export default {
  data() {
    return {
      mockTaskData: {
        taskId: 1,
        taskTitle: '测试任务',
        taskDesc: '这是一个测试任务',
        taskImages: [
          {
            imageId: 1,
            imageUrl: '/upload/2025/01/19/test1.jpg',
            imageName: '测试图片1.jpg'
          },
          {
            imageId: 2,
            imageUrl: '/upload/2025/01/19/test2.jpg',
            imageName: '测试图片2.jpg'
          }
        ]
      },
      processedImages: []
    }
  },
  
  onLoad() {
    this.testImageProcessing()
  },
  
  methods: {
    getImageUrl,
    
    testImageProcessing() {
      console.log('开始测试图片处理...')
      
      // 模拟编辑页面的图片处理逻辑
      let images = []
      if (this.mockTaskData.taskImages && this.mockTaskData.taskImages.length > 0) {
        console.log('原始taskImages数据:', this.mockTaskData.taskImages)
        images = this.mockTaskData.taskImages.map(img => {
          console.log('处理图片:', img)
          return {
            url: img.imageUrl,
            name: img.imageName || ''
          }
        })
      } else {
        console.log('没有找到taskImages数据')
      }
      
      console.log('处理后的图片数据:', images)
      this.processedImages = images
      
      uni.showToast({
        title: '图片处理完成',
        icon: 'success'
      })
    },
    
    onImageError(e) {
      console.warn('图片加载失败:', e)
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      })
    },
    
    goToEditPage() {
      uni.navigateTo({
        url: `/pages/task/publish?id=${this.mockTaskData.taskId}`
      })
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20rpx;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
}

.test-case {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.case-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.mock-data {
  background: #fff;
  padding: 15rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  white-space: pre-wrap;
}

.processed-images {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.test-image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 8rpx;
}

.image-info {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  text-align: center;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.result-item {
  font-size: 26rpx;
  color: #52c41a;
}

.test-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}
</style>
